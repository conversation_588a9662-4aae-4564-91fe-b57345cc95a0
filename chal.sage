from random import randint

mask = 7914424199261124662862774159390417416144942331617401794326681839046700982102459500083290436028119146970393378533178868190994617721884386374777744925799997

class LFSR:
    def __init__(self, mask, seed):
        self.mask = mask
        self.seed = seed
        for i in range(512):
            self.next()

    def next(self):
        self.seed = (self.seed << 1) | (int(self.seed & self.mask).bit_count() & 1)
        self.seed &= 2 ^ 512 - 1
        return self.seed % 257


flag = b'flag{*******************************************************}'
flag = flag[5:-1]

current = int.from_bytes(flag)
lfsr = LFSR(mask, current)

step = [randint(0,6) for i in range(220)]
c = []
for _ in range(220):
    for time in range(step[_]):
        lfsr.next()
    c.append(lfsr.next())
    
print(step)
print(c)