#!/usr/bin/env python3

# LFSR 密码挑战求解器

mask = 7914424199261124662862774159390417416144942331617401794326681839046700982102459500083290436028119146970393378533178868190994617721884386374777744925799997

# 从 output.txt 读取的数据
step = [1, 4, 2, 3, 3, 1, 0, 5, 6, 3, 6, 5, 6, 6, 0, 5, 6, 2, 6, 0, 2, 5, 6, 2, 5, 5, 3, 2, 4, 2, 0, 2, 4, 2, 2, 3, 0, 5, 0, 2, 0, 5, 2, 3, 1, 3, 6, 4, 4, 1, 2, 1, 2, 2, 6, 2, 6, 1, 2, 4, 6, 2, 3, 1, 6, 0, 5, 3, 3, 0, 4, 1, 5, 2, 3, 3, 2, 5, 2, 6, 5, 1, 3, 5, 6, 4, 5, 6, 3, 3, 2, 5, 5, 2, 0, 2, 6, 6, 1, 2, 2, 2, 0, 2, 4, 1, 4, 1, 4, 3, 2, 0, 0, 4, 4, 0, 3, 2, 2, 5, 2, 5, 4, 5, 3, 6, 2, 4, 5, 2, 5, 5, 5, 4, 5, 1, 0, 5, 6, 3, 4, 2, 6, 4, 2, 0, 2, 2, 3, 0, 2, 6, 5, 1, 6, 4, 0, 6, 6, 4, 0, 6, 1, 4, 2, 1, 3, 2, 0, 5, 3, 6, 3, 2, 4, 5, 1, 2, 1, 3, 5, 5, 3, 1, 1, 2, 0, 2, 3, 3, 0, 5, 1, 4, 1, 2, 2, 5, 5, 6, 2, 5, 5, 5, 1, 0, 5, 0, 4, 1, 5, 0, 5, 2, 0, 5, 0, 6, 5, 1, 6, 4, 2, 5, 3, 5, 1, 3, 5, 2, 4, 3, 2, 0, 4, 6, 6, 1, 4, 4, 2, 2, 0, 3, 4, 5, 6, 4, 3, 5, 5, 3, 2, 6, 6, 4, 1, 1, 6, 0, 0, 5, 1, 1, 1, 1, 0, 5, 5, 0, 2, 1, 6, 6, 6, 1, 5, 1, 2, 2, 3, 6, 5, 6, 4, 2, 0, 3, 5, 6, 1, 2, 1, 0, 2, 0, 4, 0, 2, 4]

c = [236, 105, 71, 95, 233, 159, 61, 12, 248, 101, 85, 46, 237, 13, 25, 96, 15, 121, 85, 169, 69, 50, 77, 105, 67, 152, 123, 213, 132, 25, 50, 143, 225, 3, 19, 35, 70, 120, 241, 129, 1, 25, 201, 127, 251, 164, 90, 36, 124, 236, 91, 107, 85, 162, 112, 122, 159, 120, 183, 220, 220, 215, 107, 172, 147, 36, 249, 128, 250, 243, 65, 3, 202, 74, 154, 156, 220, 186, 202, 243, 98, 136, 115, 169, 181, 138, 146, 191, 230, 72, 63, 182, 107, 89, 179, 149, 141, 4, 17, 136, 57, 202, 148, 161, 35, 141, 131, 10, 66, 20, 159, 62, 124, 88, 244, 230, 82, 144, 126, 152, 188, 176, 225, 227, 40, 235, 81, 1, 83, 150, 113, 25, 105, 16, 18, 70, 139, 150, 199, 112, 229, 29, 93, 138, 77, 154, 204, 93, 199, 141, 96, 191, 182, 215, 63, 212, 168, 157, 65, 36, 72, 241, 195, 53, 166, 150, 82, 141, 24, 244, 58, 28, 191, 243, 54, 148, 79, 116, 209, 8, 236, 211, 26, 105, 162, 9, 18, 147, 36, 54, 108, 246, 214, 174, 182, 166, 47, 202, 110, 238, 103, 187, 165, 252, 236, 215, 144, 32, 3, 13, 90, 180, 207, 116, 231, 155, 52, 92, 7, 30]

class LFSR:
    def __init__(self, mask, seed):
        self.mask = mask
        self.seed = seed
        for i in range(512):
            self.next()

    def next(self):
        self.seed = (self.seed << 1) | (int(self.seed & self.mask).bit_count() & 1)
        self.seed &= 2 ** 512 - 1  # 修复原代码的错误：2^512 -> 2**512
        return self.seed % 257

def reverse_lfsr_step(current_state, mask):
    """反向执行一步 LFSR 操作"""
    # LFSR 前进：seed = (seed << 1) | feedback_bit
    # 反向：prev_seed = current_seed >> 1，但需要确定最高位

    prev_state = current_state >> 1

    # 计算反馈位：应该等于 (prev_state & mask).bit_count() & 1
    # 但我们需要确定最高位，这需要通过当前状态的最低位来判断
    feedback_bit = current_state & 1

    if feedback_bit:
        prev_state |= (1 << 511)

    return prev_state

def brute_force_solve():
    """暴力破解方法：尝试所有可能的 flag"""
    print("使用暴力破解方法...")

    # flag 格式：flag{...}，去掉前缀后缀后长度约为 55 字符
    # 尝试常见的 flag 格式

    import string
    import itertools

    # 假设 flag 内容是可打印字符
    charset = string.ascii_letters + string.digits + '_-{}'

    # 由于搜索空间太大，我们采用更智能的方法
    # 分析 LFSR 的周期性和已知输出

    return analyze_lfsr_pattern()

def analyze_lfsr_pattern():
    """分析 LFSR 模式，寻找周期性"""
    print("分析 LFSR 输出模式...")

    # 创建一个测试 LFSR 来理解其行为
    test_seed = 12345678901234567890  # 测试种子
    test_lfsr = LFSR(mask, test_seed)

    # 生成一些输出来观察模式
    test_outputs = []
    for i in range(50):
        test_outputs.append(test_lfsr.next())

    print("测试输出前10个:", test_outputs[:10])

    # 现在尝试从已知输出反推
    return reconstruct_from_outputs()

def reconstruct_from_outputs():
    """从已知输出重构内部状态"""
    print("从已知输出重构内部状态...")

    # 关键洞察：我们知道每个输出点的 step 数和输出值
    # 可以通过约束求解来找到可能的内部状态

    # 简化方法：假设我们可以从某个点开始重构
    # 选择一个步数为0的点（不跳跃），这样更容易分析

    zero_step_indices = [i for i, s in enumerate(step) if s == 0]
    print(f"步数为0的索引: {zero_step_indices}")

    if zero_step_indices:
        idx = zero_step_indices[0]
        output_val = c[idx]
        print(f"索引 {idx}，输出值 {output_val}")

        # 在这个点，我们知道连续的输出值
        # 可以尝试重构这一段的内部状态
        return try_reconstruction_at_point(idx)

    return None

def try_reconstruction_at_point(start_idx):
    """在特定点尝试重构"""
    print(f"在索引 {start_idx} 尝试重构...")

    # 收集连续的已知输出，包括更大的步数
    known_sequence = []
    idx = start_idx

    # 收集更多数据点
    while idx < len(c):
        known_sequence.append((step[idx], c[idx]))
        idx += 1
        if len(known_sequence) >= 20:  # 收集更多序列
            break

    print(f"收集到序列长度: {len(known_sequence)}")
    print("序列前5个:", known_sequence[:5])

    # 现在尝试找到一个内部状态，使得它能产生这个序列
    return find_matching_state(known_sequence)

def find_matching_state(sequence):
    """找到能产生给定序列的内部状态"""
    print("寻找匹配的内部状态...")

    # 由于状态空间巨大，我们使用启发式方法
    # 从输出值开始，尝试可能的高位

    first_output = sequence[0][1]

    # 尝试不同的高位组合
    for high_bits in range(0, 2**24, 1000):  # 采样搜索
        candidate_state = (high_bits << 8) + first_output

        if test_state_sequence(candidate_state, sequence):
            print(f"找到匹配状态: {candidate_state}")
            return candidate_state

    print("未找到匹配状态")
    return None

def test_state_sequence(initial_state, sequence):
    """测试给定状态是否能产生指定序列"""
    current_state = initial_state

    for step_count, expected_output in sequence:
        # 执行指定步数
        for _ in range(step_count):
            current_state = (current_state << 1) | (int(current_state & mask).bit_count() & 1)
            current_state &= 2 ** 512 - 1

        # 检查输出
        actual_output = current_state % 257
        if actual_output != expected_output:
            return False

        # 再执行一次获取下一个状态
        current_state = (current_state << 1) | (int(current_state & mask).bit_count() & 1)
        current_state &= 2 ** 512 - 1

    return True

if __name__ == "__main__":
    # 尝试重构方法
    result = brute_force_solve()

    if result:
        print(f"找到可能的内部状态: {result}")

        # 现在需要反向计算到初始种子
        # 这需要考虑512次预热和所有的步数跳跃
        print("开始反向计算初始种子...")

        # TODO: 实现完整的反向计算
    else:
        print("未能找到解决方案，尝试其他方法...")
