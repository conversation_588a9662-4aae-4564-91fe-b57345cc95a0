#!/usr/bin/env python3

# LFSR 密码挑战最终求解器
# 使用数学方法和已知的 LFSR 特性

mask = 7914424199261124662862774159390417416144942331617401794326681839046700982102459500083290436028119146970393378533178868190994617721884386374777744925799997

# 从 output.txt 读取的数据
step = [1, 4, 2, 3, 3, 1, 0, 5, 6, 3, 6, 5, 6, 6, 0, 5, 6, 2, 6, 0, 2, 5, 6, 2, 5, 5, 3, 2, 4, 2, 0, 2, 4, 2, 2, 3, 0, 5, 0, 2, 0, 5, 2, 3, 1, 3, 6, 4, 4, 1, 2, 1, 2, 2, 6, 2, 6, 1, 2, 4, 6, 2, 3, 1, 6, 0, 5, 3, 3, 0, 4, 1, 5, 2, 3, 3, 2, 5, 2, 6, 5, 1, 3, 5, 6, 4, 5, 6, 3, 3, 2, 5, 5, 2, 0, 2, 6, 6, 1, 2, 2, 2, 0, 2, 4, 1, 4, 1, 4, 3, 2, 0, 0, 4, 4, 0, 3, 2, 2, 5, 2, 5, 4, 5, 3, 6, 2, 4, 5, 2, 5, 5, 5, 4, 5, 1, 0, 5, 6, 3, 4, 2, 6, 4, 2, 0, 2, 2, 3, 0, 2, 6, 5, 1, 6, 4, 0, 6, 6, 4, 0, 6, 1, 4, 2, 1, 3, 2, 0, 5, 3, 6, 3, 2, 4, 5, 1, 2, 1, 3, 5, 5, 3, 1, 1, 2, 0, 2, 3, 3, 0, 5, 1, 4, 1, 2, 2, 5, 5, 6, 2, 5, 5, 5, 1, 0, 5, 0, 4, 1, 5, 0, 5, 2, 0, 5, 0, 6, 5, 1, 6, 4, 2, 5, 3, 5, 1, 3, 5, 2, 4, 3, 2, 0, 4, 6, 6, 1, 4, 4, 2, 2, 0, 3, 4, 5, 6, 4, 3, 5, 5, 3, 2, 6, 6, 4, 1, 1, 6, 0, 0, 5, 1, 1, 1, 1, 0, 5, 5, 0, 2, 1, 6, 6, 6, 1, 5, 1, 2, 2, 3, 6, 5, 6, 4, 2, 0, 3, 5, 6, 1, 2, 1, 0, 2, 0, 4, 0, 2, 4]

c = [236, 105, 71, 95, 233, 159, 61, 12, 248, 101, 85, 46, 237, 13, 25, 96, 15, 121, 85, 169, 69, 50, 77, 105, 67, 152, 123, 213, 132, 25, 50, 143, 225, 3, 19, 35, 70, 120, 241, 129, 1, 25, 201, 127, 251, 164, 90, 36, 124, 236, 91, 107, 85, 162, 112, 122, 159, 120, 183, 220, 220, 215, 107, 172, 147, 36, 249, 128, 250, 243, 65, 3, 202, 74, 154, 156, 220, 186, 202, 243, 98, 136, 115, 169, 181, 138, 146, 191, 230, 72, 63, 182, 107, 89, 179, 149, 141, 4, 17, 136, 57, 202, 148, 161, 35, 141, 131, 10, 66, 20, 159, 62, 124, 88, 244, 230, 82, 144, 126, 152, 188, 176, 225, 227, 40, 235, 81, 1, 83, 150, 113, 25, 105, 16, 18, 70, 139, 150, 199, 112, 229, 29, 93, 138, 77, 154, 204, 93, 199, 141, 96, 191, 182, 215, 63, 212, 168, 157, 65, 36, 72, 241, 195, 53, 166, 150, 82, 141, 24, 244, 58, 28, 191, 243, 54, 148, 79, 116, 209, 8, 236, 211, 26, 105, 162, 9, 18, 147, 36, 54, 108, 246, 214, 174, 182, 166, 47, 202, 110, 238, 103, 187, 165, 252, 236, 215, 144, 32, 3, 13, 90, 180, 207, 116, 231, 155, 52, 92, 7, 30]

class LFSR:
    def __init__(self, mask, seed):
        self.mask = mask
        self.seed = seed
        for i in range(512):
            self.next()

    def next(self):
        self.seed = (self.seed << 1) | (int(self.seed & self.mask).bit_count() & 1)
        self.seed &= 2 ** 512 - 1
        return self.seed % 257

def brute_force_flag():
    """暴力破解 flag - 尝试常见格式"""
    print("开始暴力破解...")
    
    # 尝试一些常见的 flag 格式
    import hashlib
    import string
    
    # 常见的 CTF flag 模式
    patterns = [
        # 简单字符串
        "hello_world",
        "lfsr_challenge", 
        "linear_feedback_shift_register",
        "crypto_challenge",
        "reverse_engineering",
        
        # 十六进制模式
        "deadbeefcafebabe",
        "1234567890abcdef",
        "0123456789abcdef",
        
        # 重复模式
        "a" * 32,
        "b" * 32,
        "0" * 32,
        "f" * 32,
    ]
    
    for pattern in patterns:
        print(f"尝试模式: {pattern}")
        
        # 尝试不同的编码方式
        encodings = [
            pattern.encode(),
            pattern.encode('utf-8'),
            bytes.fromhex(pattern) if all(c in '0123456789abcdef' for c in pattern.lower()) and len(pattern) % 2 == 0 else None,
        ]
        
        for encoding in encodings:
            if encoding is None:
                continue
                
            try:
                if verify_solution(encoding):
                    flag_content = encoding.decode('utf-8', errors='ignore')
                    print(f"找到解决方案！")
                    print(f"Flag 内容: {flag_content}")
                    print(f"完整 flag: flag{{{flag_content}}}")
                    return True
            except:
                continue
    
    return False

def verify_solution(flag_bytes):
    """验证解决方案是否正确"""
    try:
        current = int.from_bytes(flag_bytes, 'big')
        lfsr = LFSR(mask, current)
        
        generated_c = []
        for i in range(220):
            for time in range(step[i]):
                lfsr.next()
            generated_c.append(lfsr.next())
        
        return generated_c == c
    except Exception as e:
        return False

def mathematical_approach():
    """数学方法：利用 LFSR 的线性性质"""
    print("尝试数学方法...")
    
    # LFSR 是线性的，我们可以建立方程组
    # 但是由于状态空间巨大（2^512），我们需要更聪明的方法
    
    # 分析 mask 的结构
    print(f"Mask 的二进制长度: {mask.bit_length()}")
    print(f"Mask 的汉明重量: {bin(mask).count('1')}")
    
    # 寻找可能的周期
    # 对于最大长度 LFSR，周期应该是 2^n - 1
    expected_period = 2**512 - 1
    print(f"期望周期: {expected_period}")
    
    # 由于周期太大，我们尝试寻找更短的模式
    return analyze_short_patterns()

def analyze_short_patterns():
    """分析短模式"""
    print("分析短模式...")
    
    # 寻找连续的零步数，这些位置的输出是连续的
    zero_positions = [i for i, s in enumerate(step) if s == 0]
    print(f"零步数位置: {zero_positions}")
    
    if len(zero_positions) >= 3:
        # 使用连续的输出来约束搜索
        consecutive_outputs = [c[pos] for pos in zero_positions[:10]]
        print(f"连续输出: {consecutive_outputs}")
        
        # 尝试找到能产生这些连续输出的状态
        return find_state_for_sequence(consecutive_outputs)
    
    return False

def find_state_for_sequence(outputs):
    """为给定输出序列找到状态"""
    print(f"为序列 {outputs[:5]}... 寻找状态")
    
    # 由于搜索空间太大，我们使用启发式方法
    # 尝试一些特殊的种子值
    
    special_seeds = [
        # 小数值
        1, 2, 3, 4, 5, 10, 100, 1000,
        # 特殊模式
        0x1234567890abcdef,
        0xdeadbeefcafebabe,
        0xffffffffffffffff,
        # 基于输出的启发式值
        sum(outputs),
        sum(outputs) * 1000,
        int(''.join(map(str, outputs))),
    ]
    
    for seed in special_seeds:
        if test_seed_sequence(seed, outputs):
            print(f"找到匹配种子: {seed}")
            return reconstruct_flag_from_seed(seed)
    
    return False

def test_seed_sequence(seed, expected_outputs):
    """测试种子是否能产生期望的输出序列"""
    try:
        lfsr = LFSR(mask, seed)
        
        # 跳过一些步骤到达我们感兴趣的位置
        for _ in range(100):  # 任意跳过一些步骤
            lfsr.next()
        
        # 检查是否能产生连续的期望输出
        for expected in expected_outputs:
            actual = lfsr.next()
            if actual != expected:
                return False
        
        return True
    except:
        return False

def reconstruct_flag_from_seed(seed):
    """从种子重构 flag"""
    try:
        # 将种子转换为字节
        seed_bytes = seed.to_bytes((seed.bit_length() + 7) // 8, 'big')
        
        # 尝试解码为文本
        try:
            flag_content = seed_bytes.decode('utf-8')
            print(f"重构的 flag: flag{{{flag_content}}}")
            return True
        except:
            # 尝试十六进制表示
            flag_content = seed_bytes.hex()
            print(f"重构的 flag (hex): flag{{{flag_content}}}")
            return True
    except:
        return False

if __name__ == "__main__":
    print("LFSR 密码挑战最终求解器")
    print("=" * 50)
    
    # 尝试多种方法
    methods = [
        ("暴力破解", brute_force_flag),
        ("数学方法", mathematical_approach),
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试方法: {method_name}")
        if method_func():
            print(f"方法 {method_name} 成功！")
            break
        else:
            print(f"方法 {method_name} 失败")
    else:
        print("\n所有方法都失败了。可能需要更高级的技术或更多计算资源。")
