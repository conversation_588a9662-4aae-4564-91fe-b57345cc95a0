#!/usr/bin/env python3

# LFSR 密码挑战求解器 - 版本2
# 使用更直接的方法：从已知输出构建状态空间

mask = 7914424199261124662862774159390417416144942331617401794326681839046700982102459500083290436028119146970393378533178868190994617721884386374777744925799997

# 从 output.txt 读取的数据
step = [1, 4, 2, 3, 3, 1, 0, 5, 6, 3, 6, 5, 6, 6, 0, 5, 6, 2, 6, 0, 2, 5, 6, 2, 5, 5, 3, 2, 4, 2, 0, 2, 4, 2, 2, 3, 0, 5, 0, 2, 0, 5, 2, 3, 1, 3, 6, 4, 4, 1, 2, 1, 2, 2, 6, 2, 6, 1, 2, 4, 6, 2, 3, 1, 6, 0, 5, 3, 3, 0, 4, 1, 5, 2, 3, 3, 2, 5, 2, 6, 5, 1, 3, 5, 6, 4, 5, 6, 3, 3, 2, 5, 5, 2, 0, 2, 6, 6, 1, 2, 2, 2, 0, 2, 4, 1, 4, 1, 4, 3, 2, 0, 0, 4, 4, 0, 3, 2, 2, 5, 2, 5, 4, 5, 3, 6, 2, 4, 5, 2, 5, 5, 5, 4, 5, 1, 0, 5, 6, 3, 4, 2, 6, 4, 2, 0, 2, 2, 3, 0, 2, 6, 5, 1, 6, 4, 0, 6, 6, 4, 0, 6, 1, 4, 2, 1, 3, 2, 0, 5, 3, 6, 3, 2, 4, 5, 1, 2, 1, 3, 5, 5, 3, 1, 1, 2, 0, 2, 3, 3, 0, 5, 1, 4, 1, 2, 2, 5, 5, 6, 2, 5, 5, 5, 1, 0, 5, 0, 4, 1, 5, 0, 5, 2, 0, 5, 0, 6, 5, 1, 6, 4, 2, 5, 3, 5, 1, 3, 5, 2, 4, 3, 2, 0, 4, 6, 6, 1, 4, 4, 2, 2, 0, 3, 4, 5, 6, 4, 3, 5, 5, 3, 2, 6, 6, 4, 1, 1, 6, 0, 0, 5, 1, 1, 1, 1, 0, 5, 5, 0, 2, 1, 6, 6, 6, 1, 5, 1, 2, 2, 3, 6, 5, 6, 4, 2, 0, 3, 5, 6, 1, 2, 1, 0, 2, 0, 4, 0, 2, 4]

c = [236, 105, 71, 95, 233, 159, 61, 12, 248, 101, 85, 46, 237, 13, 25, 96, 15, 121, 85, 169, 69, 50, 77, 105, 67, 152, 123, 213, 132, 25, 50, 143, 225, 3, 19, 35, 70, 120, 241, 129, 1, 25, 201, 127, 251, 164, 90, 36, 124, 236, 91, 107, 85, 162, 112, 122, 159, 120, 183, 220, 220, 215, 107, 172, 147, 36, 249, 128, 250, 243, 65, 3, 202, 74, 154, 156, 220, 186, 202, 243, 98, 136, 115, 169, 181, 138, 146, 191, 230, 72, 63, 182, 107, 89, 179, 149, 141, 4, 17, 136, 57, 202, 148, 161, 35, 141, 131, 10, 66, 20, 159, 62, 124, 88, 244, 230, 82, 144, 126, 152, 188, 176, 225, 227, 40, 235, 81, 1, 83, 150, 113, 25, 105, 16, 18, 70, 139, 150, 199, 112, 229, 29, 93, 138, 77, 154, 204, 93, 199, 141, 96, 191, 182, 215, 63, 212, 168, 157, 65, 36, 72, 241, 195, 53, 166, 150, 82, 141, 24, 244, 58, 28, 191, 243, 54, 148, 79, 116, 209, 8, 236, 211, 26, 105, 162, 9, 18, 147, 36, 54, 108, 246, 214, 174, 182, 166, 47, 202, 110, 238, 103, 187, 165, 252, 236, 215, 144, 32, 3, 13, 90, 180, 207, 116, 231, 155, 52, 92, 7, 30]

class LFSR:
    def __init__(self, mask, seed):
        self.mask = mask
        self.seed = seed
        for i in range(512):
            self.next()

    def next(self):
        self.seed = (self.seed << 1) | (int(self.seed & self.mask).bit_count() & 1)
        self.seed &= 2 ** 512 - 1
        return self.seed % 257

def verify_solution(flag_bytes):
    """验证解决方案是否正确"""
    try:
        current = int.from_bytes(flag_bytes)
        lfsr = LFSR(mask, current)
        
        generated_c = []
        for i in range(220):
            for time in range(step[i]):
                lfsr.next()
            generated_c.append(lfsr.next())
        
        return generated_c == c
    except:
        return False

def smart_search():
    """智能搜索方法"""
    print("开始智能搜索...")
    
    # 分析 flag 的可能格式
    # flag{...} 去掉前缀后缀，剩余部分转为整数
    
    # 常见的 flag 内容可能是：
    # 1. 十六进制字符串
    # 2. base64 编码
    # 3. 可读文本
    
    import string
    
    # 尝试不同长度的十六进制字符串
    hex_chars = "0123456789abcdef"
    
    # 估算 flag 长度：512位 LFSR，大约需要 64 字节 = 128 个十六进制字符
    for length in range(50, 70):  # 尝试不同长度
        print(f"尝试长度 {length} 的十六进制字符串...")
        
        # 使用启发式方法：基于输出的统计特性
        if try_hex_length(length):
            return True
    
    return False

def try_hex_length(length):
    """尝试特定长度的十六进制字符串"""
    import itertools
    
    hex_chars = "0123456789abcdef"
    
    # 由于搜索空间太大，我们使用模式匹配
    # 分析已知输出的模式，寻找可能的种子
    
    # 简化方法：尝试一些常见的模式
    common_patterns = [
        "0" * length,
        "f" * length,
        "deadbeef" * (length // 8) + "deadbeef"[:length % 8],
        "cafebabe" * (length // 8) + "cafebabe"[:length % 8],
    ]
    
    for pattern in common_patterns:
        if len(pattern) == length:
            flag_content = pattern
            flag_bytes = bytes.fromhex(flag_content)
            
            print(f"测试模式: {pattern[:20]}...")
            if verify_solution(flag_bytes):
                print(f"找到解决方案！")
                print(f"Flag 内容: {flag_content}")
                print(f"完整 flag: flag{{{flag_content}}}")
                return True
    
    return False

def analyze_constraints():
    """分析约束条件来缩小搜索空间"""
    print("分析约束条件...")
    
    # 分析输出值的分布
    print(f"输出值范围: {min(c)} - {max(c)}")
    print(f"输出值平均: {sum(c) / len(c):.2f}")
    
    # 分析步数分布
    print(f"步数范围: {min(step)} - {max(step)}")
    print(f"步数平均: {sum(step) / len(step):.2f}")
    
    # 寻找模式
    zero_steps = [i for i, s in enumerate(step) if s == 0]
    print(f"零步数位置: {zero_steps[:10]}...")
    
    # 在零步数位置，输出是连续的，这给我们更多约束
    if len(zero_steps) >= 2:
        idx1, idx2 = zero_steps[0], zero_steps[1]
        out1, out2 = c[idx1], c[idx2]
        print(f"连续输出示例: 位置{idx1}->输出{out1}, 位置{idx2}->输出{out2}")
        
        # 尝试基于这些约束进行搜索
        return constrained_search(zero_steps)
    
    return False

def constrained_search(zero_positions):
    """基于约束的搜索"""
    print("执行约束搜索...")
    
    # 使用前几个零步数位置的输出作为约束
    constraints = [(pos, c[pos]) for pos in zero_positions[:5]]
    print(f"使用约束: {constraints}")
    
    # 这里需要更复杂的算法来求解
    # 暂时返回 False，表示需要其他方法
    return False

if __name__ == "__main__":
    print("LFSR 密码挑战求解器 v2")
    print("=" * 50)
    
    # 首先分析约束
    if analyze_constraints():
        print("通过约束分析找到解决方案！")
    elif smart_search():
        print("通过智能搜索找到解决方案！")
    else:
        print("需要尝试其他方法...")
        print("提示：可能需要更深入的数学分析或更大的搜索空间")
